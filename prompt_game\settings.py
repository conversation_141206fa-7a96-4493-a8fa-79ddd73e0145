"""
Django settings for prompt_game project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-*h^-kvaz&-8&_dn&9^_=v#i9xq!0c97_h1y5@1g12o-yu^kv+b'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', '*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',  # Required for django functionality
    'django.contrib.sitemaps',  # Sitemap framework
    'corsheaders',  # Add CORS headers support
    'guardian',  # Object-level permissions
    'impersonate',  # User impersonation
    'game.apps.GameConfig',
    'corporate.apps.CorporateConfig',  # Corporate platform app
    'superadmin',  # Superadmin interface
    'whitenoise.runserver_nostatic',  # WhiteNoise for static files
]

# Site ID for django.contrib.sites
SITE_ID = 1

# Guardian settings
AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',  # Default backend
    'guardian.backends.ObjectPermissionBackend',  # Guardian backend
)

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # WhiteNoise middleware (after security, before all else)
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # Add CORS middleware
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'impersonate.middleware.ImpersonateMiddleware',  # Add impersonation middleware
    'corporate.middleware.ImpersonationCompanyMiddleware',  # Handle company switching during impersonation
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'game.middleware.AnonymousPlayerCleanupMiddleware',  # Cleanup anonymous player data
]

# CORS settings
CORS_ALLOW_ALL_ORIGINS = False  # More restrictive for production
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]
CORS_ALLOW_CREDENTIALS = True

# CSRF settings - Development mode (HTTP)
CSRF_COOKIE_SECURE = False  # False for HTTP development
CSRF_COOKIE_HTTPONLY = True  # More secure in production
CSRF_USE_SESSIONS = True  # Store CSRF token in the session instead of cookie


# Security settings - Disabled for HTTP development
SECURE_SSL_REDIRECT = False  # Disabled for HTTP development
# SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')  # Disabled for HTTP
# CSRF_COOKIE_SECURE = True  # Disabled for HTTP
SESSION_COOKIE_SECURE = False  # False for HTTP development
# SECURE_HSTS_SECONDS = 3600  # Disabled for HTTP development
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True  # Disabled for HTTP development
# SECURE_HSTS_PRELOAD = True  # Disabled for HTTP development


ROOT_URLCONF = 'prompt_game.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'corporate.context_processors.user_companies',
                'corporate.context_processors.help_url_processor',
                'corporate.context_processors.pending_users_processor',
                'corporate.context_processors.seo_settings_processor',
            ],
        },
    },
]

WSGI_APPLICATION = 'prompt_game.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Additional locations of static files
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# WhiteNoise configuration
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
WHITENOISE_MAX_AGE = 31536000  # 1 year in seconds
WHITENOISE_AUTOREFRESH = False  # Don't check for file changes in production

# Media files (User uploads)
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Login URLs
LOGIN_URL = 'corporate:corporate_login'
LOGIN_REDIRECT_URL = 'corporate:corporate_dashboard'
LOGOUT_REDIRECT_URL = 'corporate:corporate_login'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django Impersonate settings
IMPERSONATE = {
    'REDIRECT_URL': '/corporate/dashboard/',  # Where to redirect after impersonating
    'REQUIRE_SUPERUSER': True,  # Only superusers can impersonate
    'ALLOW_SUPERUSER': True,  # Allow superusers to be impersonated
    'URI_EXCLUSIONS': [r'^admin/'],  # Don't impersonate in these paths
    'CUSTOM_USER_QUERYSET': 'superadmin.utils.get_impersonation_users',  # Function to get users that can be impersonated
    'CUSTOM_ALLOW': 'superadmin.utils.allow_impersonation',  # Function to check if impersonation is allowed
    'REDIRECT_FIELD_NAME': 'next',  # Parameter name for the redirect URL
    'HEADER_RENDER': True,  # Render the impersonation header
    'USE_HTTP_REFERER': True,  # Use HTTP referer for redirects
    'LOOKUP_TYPE': 'pk',  # How to lookup users
}

# Django-impersonate doesn't have a context processor

# Session settings
SESSION_COOKIE_AGE = 86400  # 24 hours in seconds
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_SAVE_EVERY_REQUEST = True
