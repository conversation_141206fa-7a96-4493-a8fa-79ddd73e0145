"""
Guideline-Based Evaluation Module

This module provides functions to evaluate user responses against predefined guidelines
using LLM-based assessment.
"""

import json
import logging
import re
from openai import OpenAI
from task_guidelines import get_guidelines_for_task, format_guidelines_for_prompt

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Default API settings - can be overridden with environment variables
DEFAULT_API_URL = "https://api.openai.com/v1/chat/completions"
DEFAULT_MODEL = "gpt-3.5-turbo"

def call_llm_api(prompt, max_retries=2):
    """
    Call the Gemini API with the given prompt using OpenAI-compatible interface.

    Args:
        prompt: The prompt to send to the LLM
        max_retries: Maximum number of retry attempts

    Returns:
        The generated response text or None if the API call fails
    """
    # Use the Gemini API key from environment variables
    import os
    api_key = os.environ.get("GEMINI_API_KEY", "AIzaSyDTjwdVbjGu5Q0v-dP6QsfC4IoKM36g8l8")

    retry_count = 0
    while retry_count <= max_retries:
        try:
            logging.info(f"Calling Gemini API for evaluation")

            # Initialize the OpenAI client with Gemini configuration
            client = OpenAI(api_key=api_key, base_url="https://generativelanguage.googleapis.com/v1beta/openai/")

            # Prepare messages for the API call
            messages = [
                {"role": "system", "content": "You are an expert evaluator of business communications and documents."},
                {"role": "user", "content": prompt}
            ]

            # Make the API call
            response = client.chat.completions.create(
                model="gemini-2.0-flash",  # Adjust model name as per Gemini API documentation
                messages=messages,
                temperature=0.2,
                max_tokens=1500
            )

            # Extract the response content
            content = response.choices[0].message.content

            try:
                # Attempt to parse as JSON
                return json.loads(content)
            except json.JSONDecodeError:
                logging.warning(f"Failed to parse Gemini response as JSON: {content[:100]}...")
                # Try to extract JSON from the response if it's embedded in text
                try:
                    # Look for JSON-like content between curly braces
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                        return json.loads(json_str)
                    else:
                        return {"error": "Failed to parse response as JSON", "raw_content": content}
                except Exception:
                    return {"error": "Failed to parse response as JSON", "raw_content": content}

        except Exception as e:
            logging.error(f"Error calling Gemini API: {str(e)}")
            retry_count += 1
            if retry_count <= max_retries:
                logging.info(f"Retrying API call (attempt {retry_count}/{max_retries})")

    logging.warning("Failed to get evaluation from Gemini API after maximum retries")
    return {"error": "Failed to get evaluation from Gemini API", "score": 50}

def evaluate_response_with_guidelines(user_response, task_id):
    """
    Evaluate a user's response against structured guidelines using an LLM.

    Args:
        user_response: The user's response text
        task_id: The ID of the current task

    Returns:
        A tuple containing (grade, score, detailed_feedback, section_scores)
    """
    # Get guidelines for the task
    guidelines = get_guidelines_for_task(task_id)

    # Format guidelines for the prompt
    formatted_guidelines = format_guidelines_for_prompt(guidelines)

    # Construct the evaluation prompt
    prompt = f"""
You are evaluating a response to a task. The response should follow these guidelines:

{formatted_guidelines}

User's Response:
{user_response}

Please evaluate how well the response meets each guideline section on a scale of 0-10.
For each section, provide:
1. The score (0-10)
2. Brief feedback explaining the score
3. Suggestions for improvement if needed

Then provide an overall score (0-100) and summary feedback.

You MUST return your evaluation as a valid JSON object with the following structure. Do not include any explanatory text outside the JSON structure:
{{
  "overall_score": 85,
  "summary_feedback": "Overall assessment of the response",
  "section_scores": [
    {{
      "section": "Introduction",
      "score": 8,
      "feedback": "Specific feedback about this section",
      "suggestions": "Suggestions for improvement"
    }},
    // Replace this comment with additional section objects
  ]
}}
"""

    # Call the LLM API
    evaluation_result = call_llm_api(prompt)

    if not evaluation_result or "error" in evaluation_result:
        # Fallback to basic evaluation if LLM evaluation fails
        logging.warning("Using fallback evaluation method")
        return fallback_evaluation(user_response)

    # Extract data from evaluation result
    overall_score = evaluation_result.get("overall_score", 50)
    summary_feedback = evaluation_result.get("summary_feedback", "No feedback available")
    section_scores = evaluation_result.get("section_scores", [])

    # Generate feedback details
    feedback_details = [summary_feedback]
    for section in section_scores:
        section_name = section.get("section", "Unknown section")
        section_score = section.get("score", 0)
        section_feedback = section.get("feedback", "")
        feedback_details.append(f"{section_name}: {section_score}/10 - {section_feedback}")

    # Determine grade based on overall score
    if overall_score >= 80:
        grade = "good"
    elif overall_score >= 50:
        grade = "okay"
    else:
        grade = "bad"

    return grade, overall_score, feedback_details, section_scores

def fallback_evaluation(user_response):
    """
    Fallback evaluation method when LLM evaluation fails.

    Args:
        user_response: The user's response text

    Returns:
        A tuple containing (grade, score, feedback_details, section_scores)
    """
    # Simple length-based evaluation
    response_length = len(user_response)

    if response_length > 300:
        grade = "good"
        score = 80
        feedback = "Your response is comprehensive and detailed."
    elif response_length > 150:
        grade = "okay"
        score = 60
        feedback = "Your response is adequate but could be more detailed."
    else:
        grade = "bad"
        score = 30
        feedback = "Your response is too brief and lacks detail."

    feedback_details = [feedback, f"Response length: {response_length} characters"]

    # Create a simple section score for the fallback
    section_scores = [
        {
            "section": "Overall Content",
            "score": score // 10,
            "feedback": feedback,
            "suggestions": "Add more detail and structure to your response."
        }
    ]

    return grade, score, feedback_details, section_scores
