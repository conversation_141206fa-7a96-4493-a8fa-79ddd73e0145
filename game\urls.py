from django.urls import path
from . import views
from . import company_views

app_name = 'game'

urlpatterns = [
    # Main game interface
    path('', views.index, name='index'),
    path('preview/', views.preview_page, name='preview_page'),

    # API endpoints - Django style
    path('api/start_game/', views.start_game, name='start_game'),
    path('api/submit_prompt/', views.submit_prompt, name='submit_prompt'),
    path('api/get_game_state/', views.get_game_state, name='get_game_state'),
    path('api/preview_response/', views.preview_response, name='preview_response'),
    path('api/fetch_first_task/', views.fetch_first_task, name='fetch_first_task'),
    path('api/fetch_next_task/', views.fetch_next_task, name='fetch_next_task'),
    path('api/log_error/', views.log_error, name='log_error'),
    path('api/test_switch_session/', views.test_switch_session, name='test_switch_session'),

    # Help redirect
    path('help/', views.help_redirect, name='help_redirect'),

    # Legacy API endpoints - Flask style (for backward compatibility)
    path('start_game', views.start_game, name='start_game_legacy'),
    path('submit_prompt', views.submit_prompt, name='submit_prompt_legacy'),
    path('get_game_state', views.get_game_state, name='get_game_state_legacy'),
    path('preview_response', views.preview_response, name='preview_response_legacy'),
    path('fetch_first_task', views.fetch_first_task, name='fetch_first_task_legacy'),
    path('fetch_next_task', views.fetch_next_task, name='fetch_next_task_legacy'),

    # Company management
    path('company/create/', company_views.company_create, name='company_create'),
    path('company/<slug:company_slug>/', company_views.company_dashboard, name='company_dashboard'),
    path('company/<slug:company_slug>/home/', company_views.company_home, name='company_home'),
    path('company/<slug:company_slug>/settings/', company_views.company_settings, name='company_settings'),
    path('company/<slug:company_slug>/team/', company_views.company_team, name='company_team'),
    path('company/<slug:company_slug>/courses/', company_views.company_courses, name='company_courses'),
    path('company/<slug:company_slug>/course/<int:course_id>/', company_views.company_course_detail, name='company_course_detail'),
    path('company/<slug:company_slug>/leaderboard/', company_views.company_leaderboard, name='company_leaderboard'),
]
