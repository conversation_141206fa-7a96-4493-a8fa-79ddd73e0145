# Generated by Django 5.2.3 on 2025-06-24 18:59

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        (
            "corporate",
            "0015_jobapplication_ai_analyzed_jobapplication_ai_rank_and_more",
        ),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="certification",
            name="resume",
        ),
        migrations.RemoveField(
            model_name="education",
            name="resume",
        ),
        migrations.AlterUniqueTogether(
            name="jobapplication",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="jobapplication",
            name="applicant",
        ),
        migrations.RemoveField(
            model_name="jobapplication",
            name="job",
        ),
        migrations.RemoveField(
            model_name="jobapplication",
            name="resume",
        ),
        migrations.RemoveField(
            model_name="joblisting",
            name="company",
        ),
        migrations.RemoveField(
            model_name="resume",
            name="user",
        ),
        migrations.RemoveField(
            model_name="workexperience",
            name="resume",
        ),
        migrations.DeleteModel(
            name="ApplicationRankingFactor",
        ),
        migrations.DeleteModel(
            name="Certification",
        ),
        migrations.DeleteModel(
            name="Education",
        ),
        migrations.DeleteModel(
            name="JobApplication",
        ),
        migrations.DeleteModel(
            name="JobListing",
        ),
        migrations.DeleteModel(
            name="Resume",
        ),
        migrations.DeleteModel(
            name="WorkExperience",
        ),
    ]
